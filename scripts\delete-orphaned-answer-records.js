import mysql from 'mysql2/promise';
import dotenv from "dotenv";

dotenv.config();

const deleteOrphanedAnswerRecords = async () => {
  let connection;
  
  try {
    console.log('🧹 Starting deletion of orphaned answer records...\n');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PWD || '',
      database: process.env.DB_NAME || 'propero'
    });

    console.log('✅ Connected to database');

    await connection.beginTransaction();

    // Find answer records where ques_id exists but the question doesn't exist in questions table
    console.log('🔍 Finding answer records with invalid ques_id references...');
    
    const orphanedAnswerRecords = await connection.query(`
      SELECT ans.ans_id, ans.name, ans.ques_id 
      FROM answer ans 
      LEFT JOIN questions q ON ans.ques_id = q.id 
      WHERE ans.ques_id IS NOT NULL AND q.id IS NULL
    `);
    
    console.log(`Found ${orphanedAnswerRecords[0].length} answer records with invalid ques_id references`);
    
    if (orphanedAnswerRecords[0].length > 0) {
      console.log('🗑️  Deleting answer records with invalid ques_id references in batches...');
      const batchSize = 1000;
      let deletedCount = 0;
      
      for (let i = 0; i < orphanedAnswerRecords[0].length; i += batchSize) {
        const batch = orphanedAnswerRecords[0].slice(i, i + batchSize);
        const ids = batch.map(record => `'${record.ans_id}'`).join(',');
        
        await connection.query(`DELETE FROM answer WHERE ans_id IN (${ids})`);
        deletedCount += batch.length;
        
        console.log(`   Deleted ${deletedCount}/${orphanedAnswerRecords[0].length} orphaned answer records`);
      }
      console.log(`✅ Deleted ${orphanedAnswerRecords[0].length} orphaned answer records`);
    } else {
      console.log('ℹ️  No orphaned answer records found');
    }

    await connection.commit();
    console.log('\n🎉 Cleanup completed successfully!');
    
    console.log(`📊 Summary: Cleaned up ${orphanedAnswerRecords[0].length} orphaned answer records`);
    
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('❌ Error during cleanup:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run the cleanup
deleteOrphanedAnswerRecords()
  .then(() => {
    console.log('\n✅ Database cleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Database cleanup failed:', error.message);
    process.exit(1);
  });
