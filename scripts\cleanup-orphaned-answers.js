import mysql from 'mysql2/promise';
import dotenv from "dotenv";

dotenv.config();

const cleanupOrphanedAnswers = async () => {
  let connection;
  
  try {
    console.log('🧹 Starting cleanup of orphaned answers...\n');

    // Create database connection
    connection = await mysql.createConnection({
      host:  'localhost',
      user:  'root',
      password: 'Nobisoft1234',
      database:  'propero'
    });

    console.log('✅ Connected to database');

    await connection.beginTransaction();

    // 1. Find and delete orphaned answers records (where ques_id is NULL or points to non-existent answer)
    console.log('🔍 Finding orphaned answers records...');

    const orphanedAnswers = await connection.query(`
      SELECT a.id, a.name, a.ques_id
      FROM answers a
      LEFT JOIN answer ans ON a.ques_id = ans.ans_id
      WHERE a.ques_id IS NULL OR ans.ans_id IS NULL
    `);

    console.log(`Found ${orphanedAnswers[0].length} orphaned answers records`);

    if (orphanedAnswers[0].length > 0) {
      console.log('🗑️  Deleting orphaned answers records in batches...');
      const batchSize = 1000;
      let deletedCount = 0;

      for (let i = 0; i < orphanedAnswers[0].length; i += batchSize) {
        const batch = orphanedAnswers[0].slice(i, i + batchSize);
        const ids = batch.map(record => `'${record.id}'`).join(',');

        await connection.query(`DELETE FROM answers WHERE id IN (${ids})`);
        deletedCount += batch.length;

        console.log(`   Deleted ${deletedCount}/${orphanedAnswers[0].length} orphaned answers records`);
      }
      console.log(`✅ Deleted ${orphanedAnswers[0].length} orphaned answers records`);
    }

    // 2. Find and delete orphaned answer records (where ques_id is NULL or points to non-existent question)
    console.log('🔍 Finding orphaned answer records...');

    const orphanedAnswerRecords = await connection.query(`
      SELECT ans.ans_id, ans.name, ans.ques_id
      FROM answer ans
      LEFT JOIN questions q ON ans.ques_id = q.id
      WHERE ans.ques_id IS NULL OR q.id IS NULL
    `);

    console.log(`Found ${orphanedAnswerRecords[0].length} orphaned answer records`);

    if (orphanedAnswerRecords[0].length > 0) {
      console.log('🗑️  Deleting orphaned answer records...');
      await connection.query(`
        DELETE ans FROM answer ans
        LEFT JOIN questions q ON ans.ques_id = q.id
        WHERE ans.ques_id IS NULL OR q.id IS NULL
      `);
      console.log(`✅ Deleted ${orphanedAnswerRecords[0].length} orphaned answer records`);
    }

    // 3. Find and delete questions that reference non-existent groups
    console.log('🔍 Finding questions with invalid group references...');

    const questionsWithInvalidGroups = await connection.query(`
      SELECT q.id, q.name, q.gr_id
      FROM questions q
      LEFT JOIN \`groups\` g ON q.gr_id = g.id
      WHERE q.gr_id IS NOT NULL AND g.id IS NULL
    `);

    console.log(`Found ${questionsWithInvalidGroups[0].length} questions with invalid group references`);

    if (questionsWithInvalidGroups[0].length > 0) {
      console.log('🗑️  Deleting questions with invalid group references...');
      await connection.query(`
        DELETE q FROM questions q
        LEFT JOIN \`groups\` g ON q.gr_id = g.id
        WHERE q.gr_id IS NOT NULL AND g.id IS NULL
      `);
      console.log(`✅ Deleted ${questionsWithInvalidGroups[0].length} questions with invalid group references`);
    }

    // 4. Find orphaned groups but check if they have dependent questions first
    console.log('🔍 Finding orphaned groups...');

    const orphanedGroups = await connection.query(`
      SELECT g.id, g.gr_name, g.tem_id
      FROM \`groups\` g
      LEFT JOIN templates t ON g.tem_id = t.id
      LEFT JOIN questionaires q ON g.tem_id = q.id
      WHERE g.tem_id IS NULL OR (t.id IS NULL AND q.id IS NULL)
    `);

    console.log(`Found ${orphanedGroups[0].length} orphaned groups`);

    if (orphanedGroups[0].length > 0) {
      // Before deleting groups, check if any questions still reference them
      console.log('🔍 Checking for questions that still reference orphaned groups...');

      const groupIds = orphanedGroups[0].map(g => `'${g.id}'`).join(',');
      const dependentQuestions = await connection.query(`
        SELECT q.id, q.name, q.gr_id
        FROM questions q
        WHERE q.gr_id IN (${groupIds})
      `);

      console.log(`Found ${dependentQuestions[0].length} questions still referencing orphaned groups`);

      if (dependentQuestions[0].length > 0) {
        console.log('🗑️  Deleting questions that reference orphaned groups...');
        await connection.query(`
          DELETE FROM questions WHERE gr_id IN (${groupIds})
        `);
        console.log(`✅ Deleted ${dependentQuestions[0].length} dependent questions`);
      }

      console.log('🗑️  Deleting orphaned groups...');
      await connection.query(`
        DELETE g FROM \`groups\` g
        LEFT JOIN templates t ON g.tem_id = t.id
        LEFT JOIN questionaires q ON g.tem_id = q.id
        WHERE g.tem_id IS NULL OR (t.id IS NULL AND q.id IS NULL)
      `);
      console.log(`✅ Deleted ${orphanedGroups[0].length} orphaned groups`);
    }

    // 5. Find and delete questions with NULL gr_id (orphaned questions)
    console.log('🔍 Finding orphaned questions (NULL gr_id)...');

    const orphanedQuestions = await connection.query(`
      SELECT q.id, q.name, q.gr_id
      FROM questions q
      WHERE q.gr_id IS NULL
    `);

    console.log(`Found ${orphanedQuestions[0].length} orphaned questions`);

    if (orphanedQuestions[0].length > 0) {
      console.log('🗑️  Deleting orphaned questions...');
      await connection.query(`
        DELETE FROM questions WHERE gr_id IS NULL
      `);
      console.log(`✅ Deleted ${orphanedQuestions[0].length} orphaned questions`);
    }

    // 5. Specifically check for the problematic ID
    console.log('🔍 Checking for specific problematic ID: e97b6c58-141d-4ceb-84c3-a1bfd0f76beb');
    
    const problematicRecord = await connection.query(`
      SELECT * FROM answers WHERE id = 'e97b6c58-141d-4ceb-84c3-a1bfd0f76beb'
    `);
    
    if (problematicRecord[0].length > 0) {
      console.log('🎯 Found problematic record:', problematicRecord[0][0]);
      console.log('🗑️  Deleting problematic record...');
      await connection.query(`
        DELETE FROM answers WHERE id = 'e97b6c58-141d-4ceb-84c3-a1bfd0f76beb'
      `);
      console.log('✅ Deleted problematic record');
    } else {
      console.log('ℹ️  Problematic record not found (may have been cleaned up already)');
    }

    await connection.commit();
    console.log('\n🎉 Cleanup completed successfully!');
    
    // Show summary
    const totalCleaned = orphanedAnswers[0].length + orphanedAnswerRecords[0].length +
                        questionsWithInvalidGroups[0].length + orphanedGroups[0].length +
                        orphanedQuestions[0].length;
    console.log(`📊 Summary: Cleaned up ${totalCleaned} orphaned records total`);
    
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('❌ Error during cleanup:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run the cleanup
cleanupOrphanedAnswers()
  .then(() => {
    console.log('\n✅ Database cleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Database cleanup failed:', error.message);
    process.exit(1);
  });
